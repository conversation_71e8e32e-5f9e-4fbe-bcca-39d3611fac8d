const puppeteer = require('puppeteer');
const fs = require('fs');
const fsp = fs.promises;
const config = require("./config");
const WAIT_FOR_CLICK_SHOW_MORE_LINK_MS = config.wait_for_click_show_more_link_ms || 3500

async function waitForXPathWithTimeout(page, xpath, timeout) {
  const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Timeout')), timeout)
  );
  const elementPromise = page.evaluate((xpath) => {
    const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
    return result.snapshotLength;
  }, xpath);

  try {
    const count = await Promise.race([elementPromise, timeoutPromise]);
    return { length: count || 0, xpath: xpath, page: page };
  } catch (error) {
    console.log('Timeout or error in waitForXPathWithTimeout:', error.message);
    return { length: 0, xpath: xpath, page: page };
  }
}

// Helper function to replace page.$x() - returns count and provides click functionality
async function findElementsByXPath(page, xpath) {
  try {
    const count = await page.evaluate((xpath) => {
      const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
      return result.snapshotLength;
    }, xpath);

    // Return an array-like object with length property for compatibility
    return {
      length: count,
      xpath: xpath,
      page: page
    };
  } catch (error) {
    console.log('Error in findElementsByXPath:', error.message);
    return { length: 0, xpath: xpath, page: page };
  }
}

// Helper function to click element by xpath
async function clickElementByXPath(page, xpath, index = 0) {
  return await page.evaluate((xpath, index) => {
    const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
    if (result.snapshotLength > index) {
      const element = result.snapshotItem(index);
      element.click();
      return true;
    }
    return false;
  }, xpath, index);
}

const getFBRawData = (post_url) => new Promise((resolve, reject) => {
  //先刪除已存在檔案
  try {
    fs.unlinkSync('./tmp/result.png');
    fs.unlinkSync('./tmp/result0.html');
    fs.unlinkSync('./tmp/result1.html');
    fs.unlinkSync('./tmp/result2.txt');
    fs.unlinkSync('./tmp/customer_replies_data_2.json');
  } catch (e){
    //console.error('cannot delete tempfile');
  }
  
  //開始計時
  var start_time = new Date();
  
  //開始爬蟲作業
  (async () => {
    //const options = {"headless": config.debug == true ? false : true, "defaultViewport": null}
    const options = {
      "headless": (config.headless !== undefined ? config.headless : false),
      "defaultViewport": null, // Use full available screen size
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--start-maximized', // Start browser maximized
        //'--start-fullscreen' // Start in fullscreen mode
      ]
    }
    const browser = await puppeteer.launch(options);
    const page = await browser.newPage();

    //resolution - set to full screen
    /*await page.setViewport({
      width: 1920,
      height: 1080,
      deviceScaleFactor: 1
    });*/

    // Maximize the browser window
    await page.evaluate(() => {
      window.moveTo(0, 0);
      window.resizeTo(screen.width, screen.height);
    });
  
    //關閉通知避免跳出來干擾
    const context = browser.defaultBrowserContext();
    context.overridePermissions("https://www.facebook.com", ["geolocation", "notifications"]);
  
    for (let dir of ['./cookies', './screenshots', './tmp/'])
      !fs.existsSync(dir) && fs.mkdirSync(dir);
  
    //apply if cookie exists
    if (fs.existsSync('./cookies/cookies.json')) {
      console.log('套用已存在的 cookie');
      const cookiesString = await fsp.readFile('./cookies/cookies.json');
      const saved_cookies = JSON.parse(cookiesString);
      for (const cookie of saved_cookies) {
        await page.setCookie(cookie);
      }
    }
    try {
      await page.deleteCookie({ name: 'cookie_name', domain: '.facebook.com' });
    } catch (e) {
      // Ignore cookie deletion errors
    }
    //browser to mbasic facebook
    console.log('判斷 FB 登入狀態');
    page.setDefaultNavigationTimeout(config.timeout);
    await page.goto('https://mbasic.facebook.com');

    // Take a screenshot for debugging
    await page.screenshot({path: './screenshots/after-goto-mbasic.png'});
    console.log('已截圖: after-goto-mbasic.png');

    //check if need to login (login form email field)
    let emailField = await page.$('input[name=email]')
    console.log('emailField found:', emailField !== null);
    
    //240624: 判斷是否帳號已經被登出
    //let registerButtonLinkHandler = await page.$x("");
    const registerButtonLinkHandler = await waitForXPathWithTimeout(page, "//span[contains(text(), '建立新帳號')]", 5000);
    console.log('registerButtonLinkHandler = ', registerButtonLinkHandler);
    if (registerButtonLinkHandler.length > 0) {
        console.log('registerButtonLinkHandler found');
    } else {
      console.log('registerButtonLinkHandler not found');
    }

    // Better login detection: if no email field AND no register button, we're logged in
    // OR if we have cookies and no register button (even if email field exists on some pages)
    const hasValidCookies = fs.existsSync('./cookies/cookies.json');
    const isLoggedIn = (emailField == null && !registerButtonLinkHandler.length) ||
                       (hasValidCookies && !registerButtonLinkHandler.length);

    console.log('Login detection:');
    console.log('- hasValidCookies:', hasValidCookies);
    console.log('- emailField == null:', emailField == null);
    console.log('- registerButtonLinkHandler.length:', registerButtonLinkHandler.length);
    console.log('- isLoggedIn:', isLoggedIn);

    if (isLoggedIn) {
      //already logged in
      console.log('✅ 登入成功 (使用已存在的 cookie)');
      console.log('跳轉到 '+post_url+' ...');
      await page.goto(post_url);

      //開始執行
      console.log('開始執行爬蟲作業');

      //檢測及切換「最相關留言」->「所有留言」
      console.log('檢測「最相關留言」或「最熱門留言」');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      let linkHandlers = await findElementsByXPath(page, "//span[contains(text(), '最相關')]");
      let linkHandlers2 = await findElementsByXPath(page, "//span[contains(text(), '最熱門留言')]");
      //await page.waitFor(2500);
      
      if (linkHandlers && linkHandlers.length && linkHandlers.length > 0) {
        //切換到「所有留言」
        console.log('檢測到「最相關」，正在切換到所有留言');
        await clickElementByXPath(page, "//span[contains(text(), '最相關')]", 0);
        await new Promise(resolve => setTimeout(resolve, 3500));

        //所有留言
        linkHandlers = await findElementsByXPath(page, "//span[text()='所有留言']");
        if (linkHandlers.length > 0) {
          await clickElementByXPath(page, "//span[text()='所有留言']", 0);
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

      } else if (linkHandlers2 && linkHandlers2.length && linkHandlers2.length > 0) {
        //切換到「所有留言」
        console.log('檢測到「最熱門留言」，正在切換到所有留言');
        await clickElementByXPath(page, "//span[contains(text(), '最熱門留言')]", 0);
        await new Promise(resolve => setTimeout(resolve, 3500));

        //所有留言
        linkHandlers = await findElementsByXPath(page, "//span[text()='所有留言']");
        if (linkHandlers.length > 0) {
          await clickElementByXPath(page, "//span[text()='所有留言']", 0);
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

      } else {
        //沒有「最相關留言」，直接開始展開所有留言
        console.log('沒有「最相關留言」，直接開始展開所有留言');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
        
          //step1-展開所有留言 (loop)
          let page_counter = 0;
          //上面的「顯示先前的留言」、「檢視另xx則留言」
          //下面的「查看更多留言」
          const link_handler_keywords = ['顯示先前的留言', '檢視另', '查看更多留言','則先前的留言', '查看另', '查看先前的回答','個之前的答案'];
          
          //找上一頁 (顯示先前的留言，檢視另50則留言...等) 的連結
          //for (let i=0; i<link_handler_keywords.length; i++) {
            let i = 0;
            while(true) {
                let page_link_str = link_handler_keywords[i];
                let page_link_xpath_exp = `//span[contains(text(), '${page_link_str}')]`;
                console.log('尋找跳頁連結 - 關鍵字「' + page_link_xpath_exp + '」');
                linkHandlers = await findElementsByXPath(page, page_link_xpath_exp);
            
                await page.screenshot({path: `./screenshots/result-when-determine-${page_counter}.png`});

                if (linkHandlers.length > 0) {
                    page_counter++;
                    console.log('仍有留言待展開，正在展開上/下一頁的留言，請稍候 - 點擊「' + page_link_str + '」，點擊次數 = '+page_counter);
                    
                    // adjust font size for 125 dpi workaround
                    //await page.evaluate(() => { document.evaluate(page_link_xpath_exp, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).style.fontSize = '48px'; });
                    
                    /*let link = linkHandlers[0]
                    let linkPos = await page.evaluate((link) => {
                      link.style.fontSize = '48px'
                      link.style.fontWeight = 'bold'
                      link.style.color = 'red'
                      const {top, left} = link.getBoundingClientRect();
                      return {top, left};
                    }, link);*/

                  //await page.mouse.move(linkPos.left + 5, linkPos.top + 5); 
                  //await page.mouse.move(10, 10); 

                    //每次點選後等一小段時間
                    await page.screenshot({path: `./screenshots/result-before-click-${page_counter}.png`});
                    await clickElementByXPath(page, page_link_xpath_exp, 0);
                    await page.screenshot({path: `./screenshots/result-during-click-${page_counter}.png`});
                    await new Promise(resolve => setTimeout(resolve, WAIT_FOR_CLICK_SHOW_MORE_LINK_MS));
                    await page.screenshot({path: `./screenshots/result-after-click-${page_counter}.png`});
                    i = 0;
                    continue;
                }

                i++;
                if (i==link_handler_keywords.length) break;
            }
            console.log('已經展開所有留言頁數-舊方法');

            await page.screenshot({path: './screenshots/result-debug1.png'});
            if (!(linkHandlers && linkHandlers.length)) {
              console.log('發現 FB ******** 更新, 改採捲動跳頁方式');
              page_counter = 0;
              // 捲動到底部的時候，將此設為 true 並偵測是否連續超過一定次數沒有變化
              let get_same_replies_array_length_count = 0;
              const MAX_SAME_REPLIES_ARRAY_LENGTH_COUNT = 10; // 超過這個次數就認為已經到底了
              while (true) {
                console.log('FB ******** 更新 捲動跳頁 loop');
                let all_replies_array = await page.$$('a[aria-hidden="true"]');
                let all_replies_array_before_scroll_length = all_replies_array.length;
                let all_replies_array_after_scroll_length = 0;
                console.log('all_replies_array_before_scroll_length = ', all_replies_array_before_scroll_length);
                console.log('triggerring scrolling...')
                await page.evaluate(() => {
                  // scroll
                  document.querySelectorAll('a[aria-hidden="true"]')[document.querySelectorAll('a[aria-hidden="true"]').length - 1].scrollIntoView();
                });
                console.log('wait for 0.5 sec');
                await new Promise(resolve => setTimeout(resolve, 500));
                all_replies_array = await page.$$('a[aria-hidden="true"]');
                all_replies_array_after_scroll_length = all_replies_array.length;

                console.log('all_replies_array_after_scroll_length = ', all_replies_array_after_scroll_length);
                // if the length is different we assume new page is extended, and try to extend another new page
                if (all_replies_array_before_scroll_length === all_replies_array_after_scroll_length) {
                  console.log(`all_replies_array_before_scroll_length === all_replies_array_after_scroll_length (${all_replies_array_after_scroll_length}) / ${all_replies_array_before_scroll_length}), get_same_replies_array_length_count = ${get_same_replies_array_length_count}`);
                  //debug: need to find another way to determine if we are at the end of the page
                  get_same_replies_array_length_count++;
                  if (get_same_replies_array_length_count >= MAX_SAME_REPLIES_ARRAY_LENGTH_COUNT) {
                    console.log(`留言數量已經連續 ${MAX_SAME_REPLIES_ARRAY_LENGTH_COUNT} 次沒有變化，認為已經到達頁面底部，跳出捲動跳頁 loop`);
                    break;
                  }
                  //console.log('break loop');
                  //break;
                } else {
                  //console.log(`all_replies_array_before_scroll_length (${all_replies_array_before_scroll_length}) !== all_replies_array_after_scroll_length (${all_replies_array_after_scroll_length}), continue loop`);
                  get_same_replies_array_length_count = 0; // reset count if we found new replies
                }
                page_counter++;
                console.log(`[account_name] 仍有留言待展開，正在往下捲動 (捲動跳頁 loop)，請稍候 - 點擊次數 = ${page_counter}`);
              }

              console.log('*** FB ******** 更新, 捲動跳頁已至頁面最底端');
            }
          console.log('已經展開所有留言頁數-新方法');

            //debug
            console.log('wait 5 sec');
            await new Promise(resolve => setTimeout(resolve, 5000));
            await page.screenshot({path: './screenshots/result-debug2.png'});

            
            
          
          //step2-點選任何留言中有「查看更多」字樣的連結以展開留言 (loop)
          console.log('展開留言中的「查看更多」字樣連結');
          let has_more_span = true;
          let has_more_counter = 0;
          while (has_more_span) {
            
            linkHandlers = await findElementsByXPath(page, "//div[text()='查看更多']");
            if (linkHandlers.length > 0) {
              has_more_counter++;
              console.log('正在尋找所有留言的「查看更多」並點選以展開，請稍候 - 「查看更多」 點擊次數 = '+has_more_counter);
              //await linkHandlers[0].click();
              try {
                //await linkHandlers[0].click();
                await page.evaluate(async () => {
                  let element = document.evaluate("//div[text()='查看更多']", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                  if (element) {
                    //console.log('wait for scroll into view');
                    await element.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
                    //console.log('wait for click');
                    element.click();
                    //console.log('click ok')
                    await element.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
                  }
                });
              } catch (e) {
                console.error("(node detached) cannot click link handler! error=")
                console.error(e)
                //force exit
                console.error("(node detached) force exit")
                break;
              }
              await new Promise(resolve => setTimeout(resolve, 1500));
            }else {
              break;
            }
          }
  
          console.log('全部留言已展開完成');
  
          //test screenshoot
          console.log('正在儲存 FB 網頁畫面截圖 ...');
          await page.screenshot({path: './screenshots/result.png'});
          
          //test output html (whole html)
          console.log('正在儲存 page_html ...');
          let page_html = await page.content();
          fs.writeFileSync('./tmp/result0.html', page_html.toString(), 'utf8');
  
          //write file (body only)
          console.log('正在儲存 inner_html ...');
          let inner_html = await page.evaluate(() => document.querySelector('body').innerHTML);
          fs.writeFileSync('./tmp/result1.html', inner_html.toString(), 'utf8');

          //try to get innerText
          console.log('正在儲存 inner_text ...');
          let inner_text = await page.evaluate(() => document.querySelector('body').innerText)
          fs.writeFileSync('./tmp/result2.txt', inner_text.toString(), 'utf8');
  
          //crawler done
          console.log ('第 1 階段 - 完成\n');
  
          var end_time = new Date() - start_time;
          console.info('crawler execution time: %d seconds', end_time/1000)
          //shutdown
          await browser.close();
          resolve(true);
  
    } else {
      //need to login
      if (registerButtonLinkHandler.length) {
        console.log('\n* 偵測到爬蟲可能已被登出，嘗試自動重新登入中...');
          //先刪除已存在檔案
          try {
            console.log('delete cookie dir...');
            //rmSync is not working for Node.js v12 = =
            fs.rmdirSync('./cookie', { recursive: true, force: true });
            console.log('try to remove all applied cookies from browser instance...');
            try {
              const cookies = await page.cookies();
              for (const cookie of cookies) {
                await page.deleteCookie(cookie);
              }
            } catch (e) {
              console.log('Error clearing cookies:', e.message);
            }

            console.log('redirect to mbasic.facebook.com again and wait for emailField to be detected');
            await page.goto('https://mbasic.facebook.com');

            // need to get element again
            emailField = await page.$('input[name=email]')
          } catch (e){
            console.error(e);
          }
      }
  
      //...
      if (config.username == "" || config.password == "" || config.username == "FB帳號" || config.password == "FB密碼" || !config.username || !config.password) {
        //shutdown
        await browser.close();
        reject('[沒有已存的 cookie] 爬蟲需要登入，但是尚未於設定檔填寫 FB 登入的帳號密碼, 請檢查 config 設定檔是否已正確輸入需登入的帳號密碼');
      }
  
      //fill in login form
      if (!emailField) {
        console.error('❌ 找不到 email 輸入欄位');
        await page.screenshot({path: './screenshots/login-error-no-email.png'});
        await browser.close();
        reject('找不到 email 輸入欄位，可能 Facebook 登入頁面已改變');
        return;
      }

      await emailField.click()
      await emailField.type(config.username || username)
      await emailField.dispose()

      const passwordField = await page.$('input[name=pass]')
      if (!passwordField) {
        console.error('❌ 找不到密碼輸入欄位');
        await page.screenshot({path: './screenshots/login-error-no-password.png'});
        await browser.close();
        reject('找不到密碼輸入欄位，可能 Facebook 登入頁面已改變');
        return;
      }

      await passwordField.click()
      await passwordField.type(config.password || password)
      await passwordField.dispose()

      //hit login
      console.log('登入 FB 中...');

      // Try multiple possible login button selectors
      let loginButton = await page.$('input[name=login]');
      if (!loginButton) {
        loginButton = await page.$('button[name=login]');
      }
      if (!loginButton) {
        loginButton = await page.$('input[type=submit]');
      }
      if (!loginButton) {
        loginButton = await page.$('button[type=submit]');
      }

      if (!loginButton) {
        console.error('❌ 找不到登入按鈕');
        console.log('正在截圖以供調試...');
        await page.screenshot({path: './screenshots/login-error-no-button.png'});

        // Log all form elements for debugging
        const formElements = await page.evaluate(() => {
          const inputs = Array.from(document.querySelectorAll('input, button'));
          return inputs.map(el => ({
            tagName: el.tagName,
            type: el.type,
            name: el.name,
            value: el.value,
            id: el.id,
            className: el.className
          }));
        });
        console.log('頁面上的表單元素:', JSON.stringify(formElements, null, 2));

        await browser.close();
        reject('找不到登入按鈕，可能 Facebook 登入頁面已改變');
        return;
      }

      await loginButton.focus()
      await loginButton.click()
      await loginButton.dispose()
      //await page.waitForNavigation({timeout: 15})
      
      console.log('wait 30 sec for login to complete...');
      await new Promise(resolve => setTimeout(resolve, 30000));

      //after login
      //hit remember login button
      console.log('detect okButton?');
      const okButton = await page.$('input[value="好"][type="submit"]');
      if (okButton) {
        await okButton.focus()
        await okButton.click()
        await okButton.dispose()
      }
      //screen shoot
      await page.screenshot({path: './screenshots/page.png'});
  
      //save cookies
      console.log('儲存 cookie 設定...');
      try {
        const cookies = await page.cookies();
        await fsp.writeFile('./cookies/cookies.json', JSON.stringify(cookies, null, 2));
        let cookie_str = "";
        for (let cookie of cookies)
          cookie_str += `${cookie.name}=${cookie.value}; `;
        await fsp.writeFile('./cookies/cookies.txt', cookie_str);
      } catch (e) {
        console.log('Warning: Could not save cookies:', e.message);
      }
      
      //shutdown
      await browser.close();
      console.log('首次執行需儲存 cookie 的設定已完成, ＼n注意：請關閉爬蟲程式視窗後，重新啟動程式，即可使用。')
      reject('首次執行需儲存 cookie 的設定已完成，請關閉爬蟲程式視窗後，重新啟動程式，即可使用。')
    }
    
  })();
  
  //workaround
  /*
  process.on('unhandledRejection', error => {
    // Will print "unhandledRejection err is not defined"
    console.log('Error: unhandledRejection', error.message);
    process.exit(1);
  });
  */

});
  
exports.getFBRawData = getFBRawData;
