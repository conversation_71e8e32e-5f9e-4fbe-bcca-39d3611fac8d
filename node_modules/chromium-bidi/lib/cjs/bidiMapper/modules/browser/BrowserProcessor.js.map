{"version": 3, "file": "BrowserProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/browser/BrowserProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAqLH,kCA6EC;AA9PD,+DAQuC;AAOvC,MAAa,gBAAgB;IAClB,iBAAiB,CAAY;IAC7B,uBAAuB,CAAyB;IAChD,mBAAmB,CAAqB;IACxC,qBAAqB,CAAuB;IAErD,YACE,gBAA2B,EAC3B,sBAA8C,EAC9C,oBAA0C,EAC1C,kBAAsC;QAEtC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IAChD,CAAC;IAED,KAAK;QACH,sDAAsD;QACtD,6DAA6D;QAC7D,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAA2B;QAE3B,gFAAgF;QAChF,sEAAsE;QACtE,qDAAqD;QACrD,yCAAyC;QAEzC,MAAM,SAAS,GAAG,MAA6C,CAAC;QAEhE,IAAI,SAAS,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAChD,IACE,SAAS,CAAC,mBAAmB,KAAK,KAAK;gBACvC,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,mBAAmB,KAAK,IAAI;gBAEtE,sEAAsE;gBACtE,MAAM,IAAI,mCAAqB,CAC7B,kHAAkH,CACnH,CAAC;QACN,CAAC;QAED,MAAM,OAAO,GAAgD,EAAE,CAAC;QAEhE,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YACpB,MAAM,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC9C,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;YACjC,CAAC;YACD,IAAI,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC5B,OAAO,CAAC,eAAe,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;aAAM,CAAC;YACN,+CAA+C;YAC/C,IAAI,MAAM,CAAC,kBAAkB,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC7C,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;YACnD,CAAC;YACD,MAAM,eAAe,GACnB,MAAM,CAAC,sBAAsB,CAAC,IAAI,SAAS,CAAC;YAC9C,IAAI,eAAe,EAAE,CAAC;gBACpB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACtD,6BAA6B,EAC7B,OAAO,CACR,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAChC,OAAO,CAAC,gBAAgB,CACzB,CAAC,mBAAmB,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC;QAEtD,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAChC,OAAO,CAAC,gBAAgB,CACzB,CAAC,iBAAiB,GAAG,MAAM,CAAC,yBAAyB,CAAC,CAAC;QAExD,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,gBAAgB;SACtC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAA2C;QAE3C,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QACvC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,sCAAwB,CAChC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QACD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,8BAA8B,EAAE;gBACvE,gBAAgB,EAAE,WAAW;aAC9B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,mKAAmK;YACnK,IAAK,GAAa,CAAC,OAAO,CAAC,UAAU,CAAC,gCAAgC,CAAC,EAAE,CAAC;gBACxE,MAAM,IAAI,wCAA0B,CAAE,GAAa,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,OAAO;YACL,YAAY,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE;SAC/D,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB;QACnC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACzD,4BAA4B,EAC5B,EAAC,QAAQ,EAAC,CACX,CAAC;QACF,OAAO;YACL,wCAAwC;YACxC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,GAAG,UAAU,CAAC,QAAQ,EAAE;YACtC,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,IAAI,QAAQ;YAChD,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC;YACrC,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;YACnC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;YAC9B,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;SAC9B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,uBAAuB;aACnD,mBAAmB,EAAE;aACrB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAE9B,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,GAAG,CACrC,iBAAiB,CAAC,GAAG,CACnB,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CACxD,CACF,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAU,CAAC;QAChD,MAAM,mBAAmB,GAAG,IAAI,KAAK,EAA4B,CAAC;QAElE,wCAAwC;QACxC,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;gBACpD,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBAC/C,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QACD,OAAO,EAAC,aAAa,EAAE,mBAAmB,EAAC,CAAC;IAC9C,CAAC;CACF;AA5JD,4CA4JC;AAED;;;GAGG;AACH,SAAgB,WAAW,CACzB,WAAuC;IAEvC,IACE,WAAW,CAAC,SAAS,KAAK,QAAQ;QAClC,WAAW,CAAC,SAAS,KAAK,QAAQ,EAClC,CAAC;QACD,8EAA8E;QAC9E,8EAA8E;QAC9E,UAAU;QACV,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,WAAW,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;QACpC,MAAM,IAAI,2CAA6B,CACrC,2DAA2D,CAC5D,CAAC;IACJ,CAAC;IAED,IAAI,WAAW,CAAC,SAAS,KAAK,YAAY,EAAE,CAAC;QAC3C,MAAM,IAAI,2CAA6B,CACrC,oDAAoD,CACrD,CAAC;IACJ,CAAC;IAED,IAAI,WAAW,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;QACvC,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,aAAa;QACb,IAAI,WAAW,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACxC,uCAAuC;YACvC,OAAO,CAAC,IAAI,CAAC,QAAQ,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,kCAAkC;QAClC,IAAI,WAAW,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,sCAAsC;YACtC,OAAO,CAAC,IAAI,CAAC,SAAS,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,cAAc;QACd,IACE,WAAW,CAAC,UAAU,KAAK,SAAS;YACpC,WAAW,CAAC,YAAY,KAAK,SAAS,EACtC,CAAC;YACD,yEAAyE;YACzE,aAAa;YACb,IAAI,WAAW,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACzC,MAAM,IAAI,sCAAwB,CAChC,mDAAmD,CACpD,CAAC;YACJ,CAAC;YACD,IACE,WAAW,CAAC,YAAY,KAAK,SAAS;gBACtC,OAAO,WAAW,CAAC,YAAY,KAAK,QAAQ;gBAC5C,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC;gBAC3C,WAAW,CAAC,YAAY,GAAG,CAAC;gBAC5B,WAAW,CAAC,YAAY,GAAG,GAAG,EAC9B,CAAC;gBACD,MAAM,IAAI,sCAAwB,CAChC,0CAA0C,CAC3C,CAAC;YACJ,CAAC;YACD,OAAO,CAAC,IAAI,CACV,cAAc,WAAW,CAAC,YAAY,MAAM,WAAW,CAAC,UAAU,EAAE,CACrE,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,mFAAmF;YACnF,+DAA+D;YAC/D,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IACD,eAAe;IACf,MAAM,IAAI,mCAAqB,CAAC,oBAAoB,CAAC,CAAC;AACxD,CAAC"}