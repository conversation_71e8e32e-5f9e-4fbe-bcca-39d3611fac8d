/**
 * Copyright 2025 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Represents a user context configurations. Each new CDP target of the given user context
 * will be configured with this.
 */
export class UserContextConfig {
    /**
     * The ID of the user context.
     */
    userContextId;
    acceptInsecureCerts;
    viewport;
    devicePixelRatio;
    geolocation;
    locale;
    screenOrientation;
    userPromptHandler;
    constructor(userContextId) {
        this.userContextId = userContextId;
    }
}
//# sourceMappingURL=UserContextConfig.js.map