/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.6.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck Some types may be circular.
import z from 'zod';
export const EventSchema = z.lazy(() => z
    .object({
    type: z.literal('event'),
})
    .and(EventDataSchema)
    .and(ExtensibleSchema));
export const CommandSchema = z.lazy(() => z
    .object({
    id: JsUintSchema,
})
    .and(CommandDataSchema)
    .and(ExtensibleSchema));
export const CommandResponseSchema = z.lazy(() => z
    .object({
    type: z.literal('success'),
    id: JsUintSchema,
    result: ResultDataSchema,
})
    .and(ExtensibleSchema));
export const EventDataSchema = z.lazy(() => z.union([
    BrowsingContextEventSchema,
    InputEventSchema,
    LogEventSchema,
    NetworkEventSchema,
    ScriptEventSchema,
]));
export const CommandDataSchema = z.lazy(() => z.union([
    BrowserCommandSchema,
    BrowsingContextCommandSchema,
    EmulationCommandSchema,
    InputCommandSchema,
    NetworkCommandSchema,
    ScriptCommandSchema,
    SessionCommandSchema,
    StorageCommandSchema,
    WebExtensionCommandSchema,
]));
export const ResultDataSchema = z.lazy(() => z.union([
    BrowsingContextResultSchema,
    EmptyResultSchema,
    NetworkResultSchema,
    ScriptResultSchema,
    SessionResultSchema,
    StorageResultSchema,
    WebExtensionResultSchema,
]));
export const EmptyParamsSchema = z.lazy(() => ExtensibleSchema);
export const MessageSchema = z.lazy(() => z.union([CommandResponseSchema, ErrorResponseSchema, EventSchema]));
export const ErrorResponseSchema = z.lazy(() => z
    .object({
    type: z.literal('error'),
    id: z.union([JsUintSchema, z.null()]),
    error: ErrorCodeSchema,
    message: z.string(),
    stacktrace: z.string().optional(),
})
    .and(ExtensibleSchema));
export const EmptyResultSchema = z.lazy(() => ExtensibleSchema);
export const ExtensibleSchema = z.lazy(() => z.record(z.string(), z.any()));
export const JsIntSchema = z
    .number()
    .int()
    .gte(-9007199254740991)
    .lte(9007199254740991);
export const JsUintSchema = z
    .number()
    .int()
    .nonnegative()
    .gte(0)
    .lte(9007199254740991);
export const ErrorCodeSchema = z.lazy(() => z.enum([
    'invalid argument',
    'invalid selector',
    'invalid session id',
    'invalid web extension',
    'move target out of bounds',
    'no such alert',
    'no such network collector',
    'no such element',
    'no such frame',
    'no such handle',
    'no such history entry',
    'no such intercept',
    'no such network data',
    'no such node',
    'no such request',
    'no such script',
    'no such storage partition',
    'no such user context',
    'no such web extension',
    'session not created',
    'unable to capture screen',
    'unable to close browser',
    'unable to set cookie',
    'unable to set file input',
    'unavailable network data',
    'underspecified storage partition',
    'unknown command',
    'unknown error',
    'unsupported operation',
]));
export const SessionCommandSchema = z.lazy(() => z.union([
    Session.EndSchema,
    Session.NewSchema,
    Session.StatusSchema,
    Session.SubscribeSchema,
    Session.UnsubscribeSchema,
]));
export var Session;
(function (Session) {
    Session.ProxyConfigurationSchema = z.lazy(() => z.union([
        Session.AutodetectProxyConfigurationSchema,
        Session.DirectProxyConfigurationSchema,
        Session.ManualProxyConfigurationSchema,
        Session.PacProxyConfigurationSchema,
        Session.SystemProxyConfigurationSchema,
    ]));
})(Session || (Session = {}));
export const SessionResultSchema = z.lazy(() => z.union([
    Session.NewResultSchema,
    Session.StatusResultSchema,
    Session.SubscribeResultSchema,
]));
(function (Session) {
    Session.CapabilitiesRequestSchema = z.lazy(() => z.object({
        alwaysMatch: Session.CapabilityRequestSchema.optional(),
        firstMatch: z.array(Session.CapabilityRequestSchema).optional(),
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.CapabilityRequestSchema = z.lazy(() => z
        .object({
        acceptInsecureCerts: z.boolean().optional(),
        browserName: z.string().optional(),
        browserVersion: z.string().optional(),
        platformName: z.string().optional(),
        proxy: Session.ProxyConfigurationSchema.optional(),
        unhandledPromptBehavior: Session.UserPromptHandlerSchema.optional(),
    })
        .and(ExtensibleSchema));
})(Session || (Session = {}));
(function (Session) {
    Session.AutodetectProxyConfigurationSchema = z.lazy(() => z
        .object({
        proxyType: z.literal('autodetect'),
    })
        .and(ExtensibleSchema));
})(Session || (Session = {}));
(function (Session) {
    Session.DirectProxyConfigurationSchema = z.lazy(() => z
        .object({
        proxyType: z.literal('direct'),
    })
        .and(ExtensibleSchema));
})(Session || (Session = {}));
(function (Session) {
    Session.ManualProxyConfigurationSchema = z.lazy(() => z
        .object({
        proxyType: z.literal('manual'),
        httpProxy: z.string().optional(),
        sslProxy: z.string().optional(),
    })
        .and(Session.SocksProxyConfigurationSchema.or(z.object({})))
        .and(z.object({
        noProxy: z.array(z.string()).optional(),
    }))
        .and(ExtensibleSchema));
})(Session || (Session = {}));
(function (Session) {
    Session.SocksProxyConfigurationSchema = z.lazy(() => z.object({
        socksProxy: z.string(),
        socksVersion: z.number().int().nonnegative().gte(0).lte(255),
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.PacProxyConfigurationSchema = z.lazy(() => z
        .object({
        proxyType: z.literal('pac'),
        proxyAutoconfigUrl: z.string(),
    })
        .and(ExtensibleSchema));
})(Session || (Session = {}));
(function (Session) {
    Session.SystemProxyConfigurationSchema = z.lazy(() => z
        .object({
        proxyType: z.literal('system'),
    })
        .and(ExtensibleSchema));
})(Session || (Session = {}));
(function (Session) {
    Session.UserPromptHandlerSchema = z.lazy(() => z.object({
        alert: Session.UserPromptHandlerTypeSchema.optional(),
        beforeUnload: Session.UserPromptHandlerTypeSchema.optional(),
        confirm: Session.UserPromptHandlerTypeSchema.optional(),
        default: Session.UserPromptHandlerTypeSchema.optional(),
        file: Session.UserPromptHandlerTypeSchema.optional(),
        prompt: Session.UserPromptHandlerTypeSchema.optional(),
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.UserPromptHandlerTypeSchema = z.lazy(() => z.enum(['accept', 'dismiss', 'ignore']));
})(Session || (Session = {}));
(function (Session) {
    Session.SubscriptionSchema = z.lazy(() => z.string());
})(Session || (Session = {}));
(function (Session) {
    Session.SubscriptionRequestSchema = z.lazy(() => z.object({
        events: z.array(z.string()).min(1),
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: z.array(Browser.UserContextSchema).min(1).optional(),
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.UnsubscribeByIdRequestSchema = z.lazy(() => z.object({
        subscriptions: z.array(Session.SubscriptionSchema).min(1),
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.UnsubscribeByAttributesRequestSchema = z.lazy(() => z.object({
        events: z.array(z.string()).min(1),
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.StatusSchema = z.lazy(() => z.object({
        method: z.literal('session.status'),
        params: EmptyParamsSchema,
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.StatusResultSchema = z.lazy(() => z.object({
        ready: z.boolean(),
        message: z.string(),
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.NewSchema = z.lazy(() => z.object({
        method: z.literal('session.new'),
        params: Session.NewParametersSchema,
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.NewParametersSchema = z.lazy(() => z.object({
        capabilities: Session.CapabilitiesRequestSchema,
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.NewResultSchema = z.lazy(() => z.object({
        sessionId: z.string(),
        capabilities: z
            .object({
            acceptInsecureCerts: z.boolean(),
            browserName: z.string(),
            browserVersion: z.string(),
            platformName: z.string(),
            setWindowRect: z.boolean(),
            userAgent: z.string(),
            proxy: Session.ProxyConfigurationSchema.optional(),
            unhandledPromptBehavior: Session.UserPromptHandlerSchema.optional(),
            webSocketUrl: z.string().optional(),
        })
            .and(ExtensibleSchema),
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.EndSchema = z.lazy(() => z.object({
        method: z.literal('session.end'),
        params: EmptyParamsSchema,
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.SubscribeSchema = z.lazy(() => z.object({
        method: z.literal('session.subscribe'),
        params: Session.SubscriptionRequestSchema,
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.SubscribeResultSchema = z.lazy(() => z.object({
        subscription: Session.SubscriptionSchema,
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.UnsubscribeSchema = z.lazy(() => z.object({
        method: z.literal('session.unsubscribe'),
        params: Session.UnsubscribeParametersSchema,
    }));
})(Session || (Session = {}));
(function (Session) {
    Session.UnsubscribeParametersSchema = z.lazy(() => z.union([
        Session.UnsubscribeByAttributesRequestSchema,
        Session.UnsubscribeByIdRequestSchema,
    ]));
})(Session || (Session = {}));
export const BrowserCommandSchema = z.lazy(() => z.union([
    Browser.CloseSchema,
    Browser.CreateUserContextSchema,
    Browser.GetClientWindowsSchema,
    Browser.GetUserContextsSchema,
    Browser.RemoveUserContextSchema,
    Browser.SetClientWindowStateSchema,
]));
export const BrowserResultSchema = z.lazy(() => z.union([
    Browser.CreateUserContextResultSchema,
    Browser.GetUserContextsResultSchema,
]));
export var Browser;
(function (Browser) {
    Browser.ClientWindowSchema = z.lazy(() => z.string());
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.ClientWindowInfoSchema = z.lazy(() => z.object({
        active: z.boolean(),
        clientWindow: Browser.ClientWindowSchema,
        height: JsUintSchema,
        state: z.enum(['fullscreen', 'maximized', 'minimized', 'normal']),
        width: JsUintSchema,
        x: JsIntSchema,
        y: JsIntSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.UserContextSchema = z.lazy(() => z.string());
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.UserContextInfoSchema = z.lazy(() => z.object({
        userContext: Browser.UserContextSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.CloseSchema = z.lazy(() => z.object({
        method: z.literal('browser.close'),
        params: EmptyParamsSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.CreateUserContextSchema = z.lazy(() => z.object({
        method: z.literal('browser.createUserContext'),
        params: Browser.CreateUserContextParametersSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.CreateUserContextParametersSchema = z.lazy(() => z.object({
        acceptInsecureCerts: z.boolean().optional(),
        proxy: Session.ProxyConfigurationSchema.optional(),
        unhandledPromptBehavior: Session.UserPromptHandlerSchema.optional(),
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.CreateUserContextResultSchema = z.lazy(() => Browser.UserContextInfoSchema);
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.GetClientWindowsSchema = z.lazy(() => z.object({
        method: z.literal('browser.getClientWindows'),
        params: EmptyParamsSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.GetClientWindowsResultSchema = z.lazy(() => z.object({
        clientWindows: z.array(Browser.ClientWindowInfoSchema),
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.GetUserContextsSchema = z.lazy(() => z.object({
        method: z.literal('browser.getUserContexts'),
        params: EmptyParamsSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.GetUserContextsResultSchema = z.lazy(() => z.object({
        userContexts: z.array(Browser.UserContextInfoSchema).min(1),
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.RemoveUserContextSchema = z.lazy(() => z.object({
        method: z.literal('browser.removeUserContext'),
        params: Browser.RemoveUserContextParametersSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.RemoveUserContextParametersSchema = z.lazy(() => z.object({
        userContext: Browser.UserContextSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.SetClientWindowStateSchema = z.lazy(() => z.object({
        method: z.literal('browser.setClientWindowState'),
        params: Browser.SetClientWindowStateParametersSchema,
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.SetClientWindowStateParametersSchema = z.lazy(() => z
        .object({
        clientWindow: Browser.ClientWindowSchema,
    })
        .and(z.union([
        Browser.ClientWindowNamedStateSchema,
        Browser.ClientWindowRectStateSchema,
    ])));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.ClientWindowNamedStateSchema = z.lazy(() => z.object({
        state: z.enum(['fullscreen', 'maximized', 'minimized']),
    }));
})(Browser || (Browser = {}));
(function (Browser) {
    Browser.ClientWindowRectStateSchema = z.lazy(() => z.object({
        state: z.literal('normal'),
        width: JsUintSchema.optional(),
        height: JsUintSchema.optional(),
        x: JsIntSchema.optional(),
        y: JsIntSchema.optional(),
    }));
})(Browser || (Browser = {}));
export const BrowsingContextCommandSchema = z.lazy(() => z.union([
    BrowsingContext.ActivateSchema,
    BrowsingContext.CaptureScreenshotSchema,
    BrowsingContext.CloseSchema,
    BrowsingContext.CreateSchema,
    BrowsingContext.GetTreeSchema,
    BrowsingContext.HandleUserPromptSchema,
    BrowsingContext.LocateNodesSchema,
    BrowsingContext.NavigateSchema,
    BrowsingContext.PrintSchema,
    BrowsingContext.ReloadSchema,
    BrowsingContext.SetViewportSchema,
    BrowsingContext.TraverseHistorySchema,
]));
export const BrowsingContextEventSchema = z.lazy(() => z.union([
    BrowsingContext.ContextCreatedSchema,
    BrowsingContext.ContextDestroyedSchema,
    BrowsingContext.DomContentLoadedSchema,
    BrowsingContext.DownloadEndSchema,
    BrowsingContext.DownloadWillBeginSchema,
    BrowsingContext.FragmentNavigatedSchema,
    BrowsingContext.HistoryUpdatedSchema,
    BrowsingContext.LoadSchema,
    BrowsingContext.NavigationAbortedSchema,
    BrowsingContext.NavigationCommittedSchema,
    BrowsingContext.NavigationFailedSchema,
    BrowsingContext.NavigationStartedSchema,
    BrowsingContext.UserPromptClosedSchema,
    BrowsingContext.UserPromptOpenedSchema,
]));
export const BrowsingContextResultSchema = z.lazy(() => z.union([
    BrowsingContext.CaptureScreenshotResultSchema,
    BrowsingContext.CreateResultSchema,
    BrowsingContext.GetTreeResultSchema,
    BrowsingContext.LocateNodesResultSchema,
    BrowsingContext.NavigateResultSchema,
    BrowsingContext.PrintResultSchema,
    BrowsingContext.TraverseHistoryResultSchema,
]));
export var BrowsingContext;
(function (BrowsingContext) {
    BrowsingContext.BrowsingContextSchema = z.lazy(() => z.string());
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.InfoListSchema = z.lazy(() => z.array(BrowsingContext.InfoSchema));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.InfoSchema = z.lazy(() => z.object({
        children: z.union([BrowsingContext.InfoListSchema, z.null()]),
        clientWindow: Browser.ClientWindowSchema,
        context: BrowsingContext.BrowsingContextSchema,
        originalOpener: z.union([
            BrowsingContext.BrowsingContextSchema,
            z.null(),
        ]),
        url: z.string(),
        userContext: Browser.UserContextSchema,
        parent: z
            .union([BrowsingContext.BrowsingContextSchema, z.null()])
            .optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LocatorSchema = z.lazy(() => z.union([
        BrowsingContext.AccessibilityLocatorSchema,
        BrowsingContext.CssLocatorSchema,
        BrowsingContext.ContextLocatorSchema,
        BrowsingContext.InnerTextLocatorSchema,
        BrowsingContext.XPathLocatorSchema,
    ]));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.AccessibilityLocatorSchema = z.lazy(() => z.object({
        type: z.literal('accessibility'),
        value: z.object({
            name: z.string().optional(),
            role: z.string().optional(),
        }),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CssLocatorSchema = z.lazy(() => z.object({
        type: z.literal('css'),
        value: z.string(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ContextLocatorSchema = z.lazy(() => z.object({
        type: z.literal('context'),
        value: z.object({
            context: BrowsingContext.BrowsingContextSchema,
        }),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.InnerTextLocatorSchema = z.lazy(() => z.object({
        type: z.literal('innerText'),
        value: z.string(),
        ignoreCase: z.boolean().optional(),
        matchType: z.enum(['full', 'partial']).optional(),
        maxDepth: JsUintSchema.optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.XPathLocatorSchema = z.lazy(() => z.object({
        type: z.literal('xpath'),
        value: z.string(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationSchema = z.lazy(() => z.string());
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.BaseNavigationInfoSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        navigation: z.union([BrowsingContext.NavigationSchema, z.null()]),
        timestamp: JsUintSchema,
        url: z.string(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationInfoSchema = z.lazy(() => BrowsingContext.BaseNavigationInfoSchema);
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ReadinessStateSchema = z.lazy(() => z.enum(['none', 'interactive', 'complete']));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptTypeSchema = z.lazy(() => z.enum(['alert', 'beforeunload', 'confirm', 'prompt']));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ActivateSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.activate'),
        params: BrowsingContext.ActivateParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ActivateParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CaptureScreenshotParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        origin: z.enum(['viewport', 'document']).default('viewport').optional(),
        format: BrowsingContext.ImageFormatSchema.optional(),
        clip: BrowsingContext.ClipRectangleSchema.optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CaptureScreenshotSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.captureScreenshot'),
        params: BrowsingContext.CaptureScreenshotParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ImageFormatSchema = z.lazy(() => z.object({
        type: z.string(),
        quality: z.number().gte(0).lte(1).optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ClipRectangleSchema = z.lazy(() => z.union([
        BrowsingContext.BoxClipRectangleSchema,
        BrowsingContext.ElementClipRectangleSchema,
    ]));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ElementClipRectangleSchema = z.lazy(() => z.object({
        type: z.literal('element'),
        element: Script.SharedReferenceSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.BoxClipRectangleSchema = z.lazy(() => z.object({
        type: z.literal('box'),
        x: z.number(),
        y: z.number(),
        width: z.number(),
        height: z.number(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CaptureScreenshotResultSchema = z.lazy(() => z.object({
        data: z.string(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CloseSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.close'),
        params: BrowsingContext.CloseParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CloseParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        promptUnload: z.boolean().default(false).optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CreateSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.create'),
        params: BrowsingContext.CreateParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CreateTypeSchema = z.lazy(() => z.enum(['tab', 'window']));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CreateParametersSchema = z.lazy(() => z.object({
        type: BrowsingContext.CreateTypeSchema,
        referenceContext: BrowsingContext.BrowsingContextSchema.optional(),
        background: z.boolean().default(false).optional(),
        userContext: Browser.UserContextSchema.optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CreateResultSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.GetTreeSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.getTree'),
        params: BrowsingContext.GetTreeParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.GetTreeParametersSchema = z.lazy(() => z.object({
        maxDepth: JsUintSchema.optional(),
        root: BrowsingContext.BrowsingContextSchema.optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.GetTreeResultSchema = z.lazy(() => z.object({
        contexts: BrowsingContext.InfoListSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.HandleUserPromptSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.handleUserPrompt'),
        params: BrowsingContext.HandleUserPromptParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.HandleUserPromptParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        accept: z.boolean().optional(),
        userText: z.string().optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LocateNodesParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        locator: BrowsingContext.LocatorSchema,
        maxNodeCount: JsUintSchema.gte(1).optional(),
        serializationOptions: Script.SerializationOptionsSchema.optional(),
        startNodes: z.array(Script.SharedReferenceSchema).min(1).optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LocateNodesSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.locateNodes'),
        params: BrowsingContext.LocateNodesParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LocateNodesResultSchema = z.lazy(() => z.object({
        nodes: z.array(Script.NodeRemoteValueSchema),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigateSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.navigate'),
        params: BrowsingContext.NavigateParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigateParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        url: z.string(),
        wait: BrowsingContext.ReadinessStateSchema.optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigateResultSchema = z.lazy(() => z.object({
        navigation: z.union([BrowsingContext.NavigationSchema, z.null()]),
        url: z.string(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.print'),
        params: BrowsingContext.PrintParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        background: z.boolean().default(false).optional(),
        margin: BrowsingContext.PrintMarginParametersSchema.optional(),
        orientation: z
            .enum(['portrait', 'landscape'])
            .default('portrait')
            .optional(),
        page: BrowsingContext.PrintPageParametersSchema.optional(),
        pageRanges: z.array(z.union([JsUintSchema, z.string()])).optional(),
        scale: z.number().gte(0.1).lte(2).default(1).optional(),
        shrinkToFit: z.boolean().default(true).optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintMarginParametersSchema = z.lazy(() => z.object({
        bottom: z.number().gte(0).default(1).optional(),
        left: z.number().gte(0).default(1).optional(),
        right: z.number().gte(0).default(1).optional(),
        top: z.number().gte(0).default(1).optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintPageParametersSchema = z.lazy(() => z.object({
        height: z.number().gte(0.0352).default(27.94).optional(),
        width: z.number().gte(0.0352).default(21.59).optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintResultSchema = z.lazy(() => z.object({
        data: z.string(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ReloadSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.reload'),
        params: BrowsingContext.ReloadParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ReloadParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        ignoreCache: z.boolean().optional(),
        wait: BrowsingContext.ReadinessStateSchema.optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.SetViewportSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.setViewport'),
        params: BrowsingContext.SetViewportParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.SetViewportParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema.optional(),
        viewport: z.union([BrowsingContext.ViewportSchema, z.null()]).optional(),
        devicePixelRatio: z.union([z.number().gt(0), z.null()]).optional(),
        userContexts: z.array(Browser.UserContextSchema).min(1).optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ViewportSchema = z.lazy(() => z.object({
        width: JsUintSchema,
        height: JsUintSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.TraverseHistorySchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.traverseHistory'),
        params: BrowsingContext.TraverseHistoryParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.TraverseHistoryParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        delta: JsIntSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.TraverseHistoryResultSchema = z.lazy(() => z.object({}));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ContextCreatedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.contextCreated'),
        params: BrowsingContext.InfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ContextDestroyedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.contextDestroyed'),
        params: BrowsingContext.InfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationStartedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.navigationStarted'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.FragmentNavigatedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.fragmentNavigated'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.HistoryUpdatedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.historyUpdated'),
        params: BrowsingContext.HistoryUpdatedParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.HistoryUpdatedParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        timestamp: JsUintSchema,
        url: z.string(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DomContentLoadedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.domContentLoaded'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LoadSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.load'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DownloadWillBeginSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.downloadWillBegin'),
        params: BrowsingContext.DownloadWillBeginParamsSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DownloadWillBeginParamsSchema = z.lazy(() => z
        .object({
        suggestedFilename: z.string(),
    })
        .and(BrowsingContext.BaseNavigationInfoSchema));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DownloadEndSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.downloadEnd'),
        params: BrowsingContext.DownloadEndParamsSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DownloadEndParamsSchema = z.lazy(() => z.union([
        BrowsingContext.DownloadCanceledParamsSchema,
        BrowsingContext.DownloadCompleteParamsSchema,
    ]));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DownloadCanceledParamsSchema = z.lazy(() => z
        .object({
        status: z.literal('canceled'),
    })
        .and(BrowsingContext.BaseNavigationInfoSchema));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DownloadCompleteParamsSchema = z.lazy(() => z
        .object({
        status: z.literal('complete'),
        filepath: z.union([z.string(), z.null()]),
    })
        .and(BrowsingContext.BaseNavigationInfoSchema));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationAbortedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.navigationAborted'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationCommittedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.navigationCommitted'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationFailedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.navigationFailed'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptClosedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.userPromptClosed'),
        params: BrowsingContext.UserPromptClosedParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptClosedParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        accepted: z.boolean(),
        type: BrowsingContext.UserPromptTypeSchema,
        userText: z.string().optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptOpenedSchema = z.lazy(() => z.object({
        method: z.literal('browsingContext.userPromptOpened'),
        params: BrowsingContext.UserPromptOpenedParametersSchema,
    }));
})(BrowsingContext || (BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptOpenedParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        handler: Session.UserPromptHandlerTypeSchema,
        message: z.string(),
        type: BrowsingContext.UserPromptTypeSchema,
        defaultValue: z.string().optional(),
    }));
})(BrowsingContext || (BrowsingContext = {}));
export const EmulationCommandSchema = z.lazy(() => z.union([
    Emulation.SetGeolocationOverrideSchema,
    Emulation.SetLocaleOverrideSchema,
    Emulation.SetScreenOrientationOverrideSchema,
]));
export var Emulation;
(function (Emulation) {
    Emulation.SetGeolocationOverrideSchema = z.lazy(() => z.object({
        method: z.literal('emulation.setGeolocationOverride'),
        params: Emulation.SetGeolocationOverrideParametersSchema,
    }));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.SetGeolocationOverrideParametersSchema = z.lazy(() => z
        .union([
        z.object({
            coordinates: z.union([
                Emulation.GeolocationCoordinatesSchema,
                z.null(),
            ]),
        }),
        z.object({
            error: Emulation.GeolocationPositionErrorSchema,
        }),
    ])
        .and(z.object({
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: z.array(Browser.UserContextSchema).min(1).optional(),
    })));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.GeolocationCoordinatesSchema = z.lazy(() => z.object({
        latitude: z.number().gte(-90).lte(90),
        longitude: z.number().gte(-180).lte(180),
        accuracy: z.number().gte(0).default(1).optional(),
        altitude: z.union([z.number(), z.null().default(null)]).optional(),
        altitudeAccuracy: z
            .union([z.number().gte(0), z.null().default(null)])
            .optional(),
        heading: z
            .union([z.number().gt(0).lt(360), z.null().default(null)])
            .optional(),
        speed: z.union([z.number().gte(0), z.null().default(null)]).optional(),
    }));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.GeolocationPositionErrorSchema = z.lazy(() => z.object({
        type: z.literal('positionUnavailable'),
    }));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.SetLocaleOverrideSchema = z.lazy(() => z.object({
        method: z.literal('emulation.setLocaleOverride'),
        params: Emulation.SetLocaleOverrideParametersSchema,
    }));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.SetLocaleOverrideParametersSchema = z.lazy(() => z.object({
        locale: z.union([z.string(), z.null()]),
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: z.array(Browser.UserContextSchema).min(1).optional(),
    }));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.SetScreenOrientationOverrideSchema = z.lazy(() => z.object({
        method: z.literal('emulation.setScreenOrientationOverride'),
        params: Emulation.SetScreenOrientationOverrideParametersSchema,
    }));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.ScreenOrientationNaturalSchema = z.lazy(() => z.enum(['portrait', 'landscape']));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.ScreenOrientationTypeSchema = z.lazy(() => z.enum([
        'portrait-primary',
        'portrait-secondary',
        'landscape-primary',
        'landscape-secondary',
    ]));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.ScreenOrientationSchema = z.lazy(() => z.object({
        natural: Emulation.ScreenOrientationNaturalSchema,
        type: Emulation.ScreenOrientationTypeSchema,
    }));
})(Emulation || (Emulation = {}));
(function (Emulation) {
    Emulation.SetScreenOrientationOverrideParametersSchema = z.lazy(() => z.object({
        screenOrientation: z.union([Emulation.ScreenOrientationSchema, z.null()]),
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: z.array(Browser.UserContextSchema).min(1).optional(),
    }));
})(Emulation || (Emulation = {}));
export const NetworkCommandSchema = z.lazy(() => z.union([
    Network.AddDataCollectorSchema,
    Network.AddInterceptSchema,
    Network.ContinueRequestSchema,
    Network.ContinueResponseSchema,
    Network.ContinueWithAuthSchema,
    Network.DisownDataSchema,
    Network.FailRequestSchema,
    Network.GetDataSchema,
    Network.ProvideResponseSchema,
    Network.RemoveDataCollectorSchema,
    Network.RemoveInterceptSchema,
    Network.SetCacheBehaviorSchema,
]));
export const NetworkEventSchema = z.lazy(() => z.union([
    Network.AuthRequiredSchema,
    Network.BeforeRequestSentSchema,
    Network.FetchErrorSchema,
    Network.ResponseCompletedSchema,
    Network.ResponseStartedSchema,
]));
export const NetworkResultSchema = z.lazy(() => Network.AddInterceptResultSchema);
export var Network;
(function (Network) {
    Network.AuthChallengeSchema = z.lazy(() => z.object({
        scheme: z.string(),
        realm: z.string(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.AuthCredentialsSchema = z.lazy(() => z.object({
        type: z.literal('password'),
        username: z.string(),
        password: z.string(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.BaseParametersSchema = z.lazy(() => z.object({
        context: z.union([BrowsingContext.BrowsingContextSchema, z.null()]),
        isBlocked: z.boolean(),
        navigation: z.union([BrowsingContext.NavigationSchema, z.null()]),
        redirectCount: JsUintSchema,
        request: Network.RequestDataSchema,
        timestamp: JsUintSchema,
        intercepts: z.array(Network.InterceptSchema).min(1).optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.DataTypeSchema = z.literal('response');
})(Network || (Network = {}));
(function (Network) {
    Network.BytesValueSchema = z.lazy(() => z.union([Network.StringValueSchema, Network.Base64ValueSchema]));
})(Network || (Network = {}));
(function (Network) {
    Network.StringValueSchema = z.lazy(() => z.object({
        type: z.literal('string'),
        value: z.string(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.Base64ValueSchema = z.lazy(() => z.object({
        type: z.literal('base64'),
        value: z.string(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.CollectorSchema = z.lazy(() => z.string());
})(Network || (Network = {}));
(function (Network) {
    Network.CollectorTypeSchema = z.literal('blob');
})(Network || (Network = {}));
(function (Network) {
    Network.SameSiteSchema = z.lazy(() => z.enum(['strict', 'lax', 'none', 'default']));
})(Network || (Network = {}));
(function (Network) {
    Network.CookieSchema = z.lazy(() => z
        .object({
        name: z.string(),
        value: Network.BytesValueSchema,
        domain: z.string(),
        path: z.string(),
        size: JsUintSchema,
        httpOnly: z.boolean(),
        secure: z.boolean(),
        sameSite: Network.SameSiteSchema,
        expiry: JsUintSchema.optional(),
    })
        .and(ExtensibleSchema));
})(Network || (Network = {}));
(function (Network) {
    Network.CookieHeaderSchema = z.lazy(() => z.object({
        name: z.string(),
        value: Network.BytesValueSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.FetchTimingInfoSchema = z.lazy(() => z.object({
        timeOrigin: z.number(),
        requestTime: z.number(),
        redirectStart: z.number(),
        redirectEnd: z.number(),
        fetchStart: z.number(),
        dnsStart: z.number(),
        dnsEnd: z.number(),
        connectStart: z.number(),
        connectEnd: z.number(),
        tlsStart: z.number(),
        requestStart: z.number(),
        responseStart: z.number(),
        responseEnd: z.number(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.HeaderSchema = z.lazy(() => z.object({
        name: z.string(),
        value: Network.BytesValueSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.InitiatorSchema = z.lazy(() => z.object({
        columnNumber: JsUintSchema.optional(),
        lineNumber: JsUintSchema.optional(),
        request: Network.RequestSchema.optional(),
        stackTrace: Script.StackTraceSchema.optional(),
        type: z.enum(['parser', 'script', 'preflight', 'other']).optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.InterceptSchema = z.lazy(() => z.string());
})(Network || (Network = {}));
(function (Network) {
    Network.RequestSchema = z.lazy(() => z.string());
})(Network || (Network = {}));
(function (Network) {
    Network.RequestDataSchema = z.lazy(() => z.object({
        request: Network.RequestSchema,
        url: z.string(),
        method: z.string(),
        headers: z.array(Network.HeaderSchema),
        cookies: z.array(Network.CookieSchema),
        headersSize: JsUintSchema,
        bodySize: z.union([JsUintSchema, z.null()]),
        destination: z.string(),
        initiatorType: z.union([z.string(), z.null()]),
        timings: Network.FetchTimingInfoSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ResponseContentSchema = z.lazy(() => z.object({
        size: JsUintSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ResponseDataSchema = z.lazy(() => z.object({
        url: z.string(),
        protocol: z.string(),
        status: JsUintSchema,
        statusText: z.string(),
        fromCache: z.boolean(),
        headers: z.array(Network.HeaderSchema),
        mimeType: z.string(),
        bytesReceived: JsUintSchema,
        headersSize: z.union([JsUintSchema, z.null()]),
        bodySize: z.union([JsUintSchema, z.null()]),
        content: Network.ResponseContentSchema,
        authChallenges: z.array(Network.AuthChallengeSchema).optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.SetCookieHeaderSchema = z.lazy(() => z.object({
        name: z.string(),
        value: Network.BytesValueSchema,
        domain: z.string().optional(),
        httpOnly: z.boolean().optional(),
        expiry: z.string().optional(),
        maxAge: JsIntSchema.optional(),
        path: z.string().optional(),
        sameSite: Network.SameSiteSchema.optional(),
        secure: z.boolean().optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.UrlPatternSchema = z.lazy(() => z.union([Network.UrlPatternPatternSchema, Network.UrlPatternStringSchema]));
})(Network || (Network = {}));
(function (Network) {
    Network.UrlPatternPatternSchema = z.lazy(() => z.object({
        type: z.literal('pattern'),
        protocol: z.string().optional(),
        hostname: z.string().optional(),
        port: z.string().optional(),
        pathname: z.string().optional(),
        search: z.string().optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.UrlPatternStringSchema = z.lazy(() => z.object({
        type: z.literal('string'),
        pattern: z.string(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.AddDataCollectorSchema = z.lazy(() => z.object({
        method: z.literal('network.addDataCollector'),
        params: Network.AddDataCollectorParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.AddDataCollectorParametersSchema = z.lazy(() => z.object({
        dataTypes: z.array(Network.DataTypeSchema).min(1),
        maxEncodedDataSize: JsUintSchema,
        collectorType: Network.CollectorTypeSchema.default('blob').optional(),
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: z.array(Browser.UserContextSchema).min(1).optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.AddDataCollectorResultSchema = z.lazy(() => z.object({
        collector: Network.CollectorSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.AddInterceptParametersSchema = z.lazy(() => z.object({
        phases: z.array(Network.InterceptPhaseSchema).min(1),
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        urlPatterns: z.array(Network.UrlPatternSchema).optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.AddInterceptSchema = z.lazy(() => z.object({
        method: z.literal('network.addIntercept'),
        params: Network.AddInterceptParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.InterceptPhaseSchema = z.lazy(() => z.enum(['beforeRequestSent', 'responseStarted', 'authRequired']));
})(Network || (Network = {}));
(function (Network) {
    Network.AddInterceptResultSchema = z.lazy(() => z.object({
        intercept: Network.InterceptSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ContinueRequestSchema = z.lazy(() => z.object({
        method: z.literal('network.continueRequest'),
        params: Network.ContinueRequestParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ContinueRequestParametersSchema = z.lazy(() => z.object({
        request: Network.RequestSchema,
        body: Network.BytesValueSchema.optional(),
        cookies: z.array(Network.CookieHeaderSchema).optional(),
        headers: z.array(Network.HeaderSchema).optional(),
        method: z.string().optional(),
        url: z.string().optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ContinueResponseSchema = z.lazy(() => z.object({
        method: z.literal('network.continueResponse'),
        params: Network.ContinueResponseParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ContinueResponseParametersSchema = z.lazy(() => z.object({
        request: Network.RequestSchema,
        cookies: z.array(Network.SetCookieHeaderSchema).optional(),
        credentials: Network.AuthCredentialsSchema.optional(),
        headers: z.array(Network.HeaderSchema).optional(),
        reasonPhrase: z.string().optional(),
        statusCode: JsUintSchema.optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ContinueWithAuthSchema = z.lazy(() => z.object({
        method: z.literal('network.continueWithAuth'),
        params: Network.ContinueWithAuthParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ContinueWithAuthParametersSchema = z.lazy(() => z
        .object({
        request: Network.RequestSchema,
    })
        .and(z.union([
        Network.ContinueWithAuthCredentialsSchema,
        Network.ContinueWithAuthNoCredentialsSchema,
    ])));
})(Network || (Network = {}));
(function (Network) {
    Network.ContinueWithAuthCredentialsSchema = z.lazy(() => z.object({
        action: z.literal('provideCredentials'),
        credentials: Network.AuthCredentialsSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ContinueWithAuthNoCredentialsSchema = z.lazy(() => z.object({
        action: z.enum(['default', 'cancel']),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.DisownDataSchema = z.lazy(() => z.object({
        method: z.literal('network.disownData'),
        params: Network.DisownDataParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.DisownDataParametersSchema = z.lazy(() => z.object({
        dataType: Network.DataTypeSchema,
        collector: Network.CollectorSchema,
        request: Network.RequestSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.FailRequestSchema = z.lazy(() => z.object({
        method: z.literal('network.failRequest'),
        params: Network.FailRequestParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.FailRequestParametersSchema = z.lazy(() => z.object({
        request: Network.RequestSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.GetDataSchema = z.lazy(() => z.object({
        method: z.literal('network.getData'),
        params: Network.GetDataParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.GetDataParametersSchema = z.lazy(() => z.object({
        dataType: Network.DataTypeSchema,
        collector: Network.CollectorSchema.optional(),
        disown: z.boolean().default(false).optional(),
        request: Network.RequestSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.GetDataResultSchema = z.lazy(() => z.object({
        bytes: Network.BytesValueSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ProvideResponseSchema = z.lazy(() => z.object({
        method: z.literal('network.provideResponse'),
        params: Network.ProvideResponseParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ProvideResponseParametersSchema = z.lazy(() => z.object({
        request: Network.RequestSchema,
        body: Network.BytesValueSchema.optional(),
        cookies: z.array(Network.SetCookieHeaderSchema).optional(),
        headers: z.array(Network.HeaderSchema).optional(),
        reasonPhrase: z.string().optional(),
        statusCode: JsUintSchema.optional(),
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.RemoveDataCollectorSchema = z.lazy(() => z.object({
        method: z.literal('network.removeDataCollector'),
        params: Network.RemoveDataCollectorParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.RemoveDataCollectorParametersSchema = z.lazy(() => z.object({
        collector: Network.CollectorSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.RemoveInterceptSchema = z.lazy(() => z.object({
        method: z.literal('network.removeIntercept'),
        params: Network.RemoveInterceptParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.RemoveInterceptParametersSchema = z.lazy(() => z.object({
        intercept: Network.InterceptSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.SetCacheBehaviorSchema = z.lazy(() => z.object({
        method: z.literal('network.setCacheBehavior'),
        params: Network.SetCacheBehaviorParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.SetCacheBehaviorParametersSchema = z.lazy(() => z.object({
        cacheBehavior: z.enum(['default', 'bypass']),
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
    }));
})(Network || (Network = {}));
export const ScriptEventSchema = z.lazy(() => z.union([
    Script.MessageSchema,
    Script.RealmCreatedSchema,
    Script.RealmDestroyedSchema,
]));
(function (Network) {
    Network.AuthRequiredParametersSchema = z.lazy(() => Network.BaseParametersSchema.and(z.object({
        response: Network.ResponseDataSchema,
    })));
})(Network || (Network = {}));
(function (Network) {
    Network.BeforeRequestSentParametersSchema = z.lazy(() => Network.BaseParametersSchema.and(z.object({
        initiator: Network.InitiatorSchema.optional(),
    })));
})(Network || (Network = {}));
(function (Network) {
    Network.FetchErrorParametersSchema = z.lazy(() => Network.BaseParametersSchema.and(z.object({
        errorText: z.string(),
    })));
})(Network || (Network = {}));
(function (Network) {
    Network.ResponseCompletedParametersSchema = z.lazy(() => Network.BaseParametersSchema.and(z.object({
        response: Network.ResponseDataSchema,
    })));
})(Network || (Network = {}));
(function (Network) {
    Network.ResponseStartedParametersSchema = z.lazy(() => Network.BaseParametersSchema.and(z.object({
        response: Network.ResponseDataSchema,
    })));
})(Network || (Network = {}));
export const ScriptCommandSchema = z.lazy(() => z.union([
    Script.AddPreloadScriptSchema,
    Script.CallFunctionSchema,
    Script.DisownSchema,
    Script.EvaluateSchema,
    Script.GetRealmsSchema,
    Script.RemovePreloadScriptSchema,
]));
export const ScriptResultSchema = z.lazy(() => z.union([
    Script.AddPreloadScriptResultSchema,
    Script.EvaluateResultSchema,
    Script.GetRealmsResultSchema,
]));
(function (Network) {
    Network.AuthRequiredSchema = z.lazy(() => z.object({
        method: z.literal('network.authRequired'),
        params: Network.AuthRequiredParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.BeforeRequestSentSchema = z.lazy(() => z.object({
        method: z.literal('network.beforeRequestSent'),
        params: Network.BeforeRequestSentParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.FetchErrorSchema = z.lazy(() => z.object({
        method: z.literal('network.fetchError'),
        params: Network.FetchErrorParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ResponseCompletedSchema = z.lazy(() => z.object({
        method: z.literal('network.responseCompleted'),
        params: Network.ResponseCompletedParametersSchema,
    }));
})(Network || (Network = {}));
(function (Network) {
    Network.ResponseStartedSchema = z.lazy(() => z.object({
        method: z.literal('network.responseStarted'),
        params: Network.ResponseStartedParametersSchema,
    }));
})(Network || (Network = {}));
export var Script;
(function (Script) {
    Script.ChannelSchema = z.lazy(() => z.string());
})(Script || (Script = {}));
(function (Script) {
    Script.EvaluateResultSuccessSchema = z.lazy(() => z.object({
        type: z.literal('success'),
        result: Script.RemoteValueSchema,
        realm: Script.RealmSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ExceptionDetailsSchema = z.lazy(() => z.object({
        columnNumber: JsUintSchema,
        exception: Script.RemoteValueSchema,
        lineNumber: JsUintSchema,
        stackTrace: Script.StackTraceSchema,
        text: z.string(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ChannelValueSchema = z.lazy(() => z.object({
        type: z.literal('channel'),
        value: Script.ChannelPropertiesSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ChannelPropertiesSchema = z.lazy(() => z.object({
        channel: Script.ChannelSchema,
        serializationOptions: Script.SerializationOptionsSchema.optional(),
        ownership: Script.ResultOwnershipSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.EvaluateResultSchema = z.lazy(() => z.union([
        Script.EvaluateResultSuccessSchema,
        Script.EvaluateResultExceptionSchema,
    ]));
})(Script || (Script = {}));
(function (Script) {
    Script.EvaluateResultExceptionSchema = z.lazy(() => z.object({
        type: z.literal('exception'),
        exceptionDetails: Script.ExceptionDetailsSchema,
        realm: Script.RealmSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.HandleSchema = z.lazy(() => z.string());
})(Script || (Script = {}));
(function (Script) {
    Script.InternalIdSchema = z.lazy(() => z.string());
})(Script || (Script = {}));
(function (Script) {
    Script.ListLocalValueSchema = z.lazy(() => z.array(Script.LocalValueSchema));
})(Script || (Script = {}));
(function (Script) {
    Script.LocalValueSchema = z.lazy(() => z.union([
        Script.RemoteReferenceSchema,
        Script.PrimitiveProtocolValueSchema,
        Script.ChannelValueSchema,
        Script.ArrayLocalValueSchema,
        Script.DateLocalValueSchema,
        Script.MapLocalValueSchema,
        Script.ObjectLocalValueSchema,
        Script.RegExpLocalValueSchema,
        Script.SetLocalValueSchema,
    ]));
})(Script || (Script = {}));
(function (Script) {
    Script.ArrayLocalValueSchema = z.lazy(() => z.object({
        type: z.literal('array'),
        value: Script.ListLocalValueSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.DateLocalValueSchema = z.lazy(() => z.object({
        type: z.literal('date'),
        value: z.string(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.MappingLocalValueSchema = z.lazy(() => z.array(z.tuple([
        z.union([Script.LocalValueSchema, z.string()]),
        Script.LocalValueSchema,
    ])));
})(Script || (Script = {}));
(function (Script) {
    Script.MapLocalValueSchema = z.lazy(() => z.object({
        type: z.literal('map'),
        value: Script.MappingLocalValueSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ObjectLocalValueSchema = z.lazy(() => z.object({
        type: z.literal('object'),
        value: Script.MappingLocalValueSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RegExpValueSchema = z.lazy(() => z.object({
        pattern: z.string(),
        flags: z.string().optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RegExpLocalValueSchema = z.lazy(() => z.object({
        type: z.literal('regexp'),
        value: Script.RegExpValueSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.SetLocalValueSchema = z.lazy(() => z.object({
        type: z.literal('set'),
        value: Script.ListLocalValueSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.PreloadScriptSchema = z.lazy(() => z.string());
})(Script || (Script = {}));
(function (Script) {
    Script.RealmSchema = z.lazy(() => z.string());
})(Script || (Script = {}));
(function (Script) {
    Script.PrimitiveProtocolValueSchema = z.lazy(() => z.union([
        Script.UndefinedValueSchema,
        Script.NullValueSchema,
        Script.StringValueSchema,
        Script.NumberValueSchema,
        Script.BooleanValueSchema,
        Script.BigIntValueSchema,
    ]));
})(Script || (Script = {}));
(function (Script) {
    Script.UndefinedValueSchema = z.lazy(() => z.object({
        type: z.literal('undefined'),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.NullValueSchema = z.lazy(() => z.object({
        type: z.literal('null'),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.StringValueSchema = z.lazy(() => z.object({
        type: z.literal('string'),
        value: z.string(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.SpecialNumberSchema = z.lazy(() => z.enum(['NaN', '-0', 'Infinity', '-Infinity']));
})(Script || (Script = {}));
(function (Script) {
    Script.NumberValueSchema = z.lazy(() => z.object({
        type: z.literal('number'),
        value: z.union([z.number(), Script.SpecialNumberSchema]),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.BooleanValueSchema = z.lazy(() => z.object({
        type: z.literal('boolean'),
        value: z.boolean(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.BigIntValueSchema = z.lazy(() => z.object({
        type: z.literal('bigint'),
        value: z.string(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RealmInfoSchema = z.lazy(() => z.union([
        Script.WindowRealmInfoSchema,
        Script.DedicatedWorkerRealmInfoSchema,
        Script.SharedWorkerRealmInfoSchema,
        Script.ServiceWorkerRealmInfoSchema,
        Script.WorkerRealmInfoSchema,
        Script.PaintWorkletRealmInfoSchema,
        Script.AudioWorkletRealmInfoSchema,
        Script.WorkletRealmInfoSchema,
    ]));
})(Script || (Script = {}));
(function (Script) {
    Script.BaseRealmInfoSchema = z.lazy(() => z.object({
        realm: Script.RealmSchema,
        origin: z.string(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.WindowRealmInfoSchema = z.lazy(() => Script.BaseRealmInfoSchema.and(z.object({
        type: z.literal('window'),
        context: BrowsingContext.BrowsingContextSchema,
        sandbox: z.string().optional(),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.DedicatedWorkerRealmInfoSchema = z.lazy(() => Script.BaseRealmInfoSchema.and(z.object({
        type: z.literal('dedicated-worker'),
        owners: z.tuple([Script.RealmSchema]),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.SharedWorkerRealmInfoSchema = z.lazy(() => Script.BaseRealmInfoSchema.and(z.object({
        type: z.literal('shared-worker'),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.ServiceWorkerRealmInfoSchema = z.lazy(() => Script.BaseRealmInfoSchema.and(z.object({
        type: z.literal('service-worker'),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.WorkerRealmInfoSchema = z.lazy(() => Script.BaseRealmInfoSchema.and(z.object({
        type: z.literal('worker'),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.PaintWorkletRealmInfoSchema = z.lazy(() => Script.BaseRealmInfoSchema.and(z.object({
        type: z.literal('paint-worklet'),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.AudioWorkletRealmInfoSchema = z.lazy(() => Script.BaseRealmInfoSchema.and(z.object({
        type: z.literal('audio-worklet'),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.WorkletRealmInfoSchema = z.lazy(() => Script.BaseRealmInfoSchema.and(z.object({
        type: z.literal('worklet'),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.RealmTypeSchema = z.lazy(() => z.enum([
        'window',
        'dedicated-worker',
        'shared-worker',
        'service-worker',
        'worker',
        'paint-worklet',
        'audio-worklet',
        'worklet',
    ]));
})(Script || (Script = {}));
(function (Script) {
    Script.ListRemoteValueSchema = z.lazy(() => z.array(Script.RemoteValueSchema));
})(Script || (Script = {}));
(function (Script) {
    Script.MappingRemoteValueSchema = z.lazy(() => z.array(z.tuple([
        z.union([Script.RemoteValueSchema, z.string()]),
        Script.RemoteValueSchema,
    ])));
})(Script || (Script = {}));
(function (Script) {
    Script.RemoteValueSchema = z.lazy(() => z.union([
        Script.PrimitiveProtocolValueSchema,
        Script.SymbolRemoteValueSchema,
        Script.ArrayRemoteValueSchema,
        Script.ObjectRemoteValueSchema,
        Script.FunctionRemoteValueSchema,
        Script.RegExpRemoteValueSchema,
        Script.DateRemoteValueSchema,
        Script.MapRemoteValueSchema,
        Script.SetRemoteValueSchema,
        Script.WeakMapRemoteValueSchema,
        Script.WeakSetRemoteValueSchema,
        Script.GeneratorRemoteValueSchema,
        Script.ErrorRemoteValueSchema,
        Script.ProxyRemoteValueSchema,
        Script.PromiseRemoteValueSchema,
        Script.TypedArrayRemoteValueSchema,
        Script.ArrayBufferRemoteValueSchema,
        Script.NodeListRemoteValueSchema,
        Script.HtmlCollectionRemoteValueSchema,
        Script.NodeRemoteValueSchema,
        Script.WindowProxyRemoteValueSchema,
    ]));
})(Script || (Script = {}));
(function (Script) {
    Script.RemoteReferenceSchema = z.lazy(() => z.union([Script.SharedReferenceSchema, Script.RemoteObjectReferenceSchema]));
})(Script || (Script = {}));
(function (Script) {
    Script.SharedReferenceSchema = z.lazy(() => z
        .object({
        sharedId: Script.SharedIdSchema,
        handle: Script.HandleSchema.optional(),
    })
        .and(ExtensibleSchema));
})(Script || (Script = {}));
(function (Script) {
    Script.RemoteObjectReferenceSchema = z.lazy(() => z
        .object({
        handle: Script.HandleSchema,
        sharedId: Script.SharedIdSchema.optional(),
    })
        .and(ExtensibleSchema));
})(Script || (Script = {}));
(function (Script) {
    Script.SymbolRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('symbol'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ArrayRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('array'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.ListRemoteValueSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ObjectRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('object'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.MappingRemoteValueSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.FunctionRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('function'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RegExpRemoteValueSchema = z.lazy(() => Script.RegExpLocalValueSchema.and(z.object({
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.DateRemoteValueSchema = z.lazy(() => Script.DateLocalValueSchema.and(z.object({
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    })));
})(Script || (Script = {}));
(function (Script) {
    Script.MapRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('map'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.MappingRemoteValueSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.SetRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('set'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.ListRemoteValueSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.WeakMapRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('weakmap'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.WeakSetRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('weakset'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.GeneratorRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('generator'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ErrorRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('error'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ProxyRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('proxy'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.PromiseRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('promise'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.TypedArrayRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('typedarray'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ArrayBufferRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('arraybuffer'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.NodeListRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('nodelist'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.ListRemoteValueSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.HtmlCollectionRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('htmlcollection'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.ListRemoteValueSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.NodeRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('node'),
        sharedId: Script.SharedIdSchema.optional(),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.NodePropertiesSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.NodePropertiesSchema = z.lazy(() => z.object({
        nodeType: JsUintSchema,
        childNodeCount: JsUintSchema,
        attributes: z.record(z.string(), z.string()).optional(),
        children: z.array(Script.NodeRemoteValueSchema).optional(),
        localName: z.string().optional(),
        mode: z.enum(['open', 'closed']).optional(),
        namespaceURI: z.string().optional(),
        nodeValue: z.string().optional(),
        shadowRoot: z.union([Script.NodeRemoteValueSchema, z.null()]).optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.WindowProxyRemoteValueSchema = z.lazy(() => z.object({
        type: z.literal('window'),
        value: Script.WindowProxyPropertiesSchema,
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.WindowProxyPropertiesSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ResultOwnershipSchema = z.lazy(() => z.enum(['root', 'none']));
})(Script || (Script = {}));
(function (Script) {
    Script.SerializationOptionsSchema = z.lazy(() => z.object({
        maxDomDepth: z.union([JsUintSchema, z.null()]).default(0).optional(),
        maxObjectDepth: z
            .union([JsUintSchema, z.null()])
            .default(null)
            .optional(),
        includeShadowTree: z
            .enum(['none', 'open', 'all'])
            .default('none')
            .optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.SharedIdSchema = z.lazy(() => z.string());
})(Script || (Script = {}));
(function (Script) {
    Script.StackFrameSchema = z.lazy(() => z.object({
        columnNumber: JsUintSchema,
        functionName: z.string(),
        lineNumber: JsUintSchema,
        url: z.string(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.StackTraceSchema = z.lazy(() => z.object({
        callFrames: z.array(Script.StackFrameSchema),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.SourceSchema = z.lazy(() => z.object({
        realm: Script.RealmSchema,
        context: BrowsingContext.BrowsingContextSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RealmTargetSchema = z.lazy(() => z.object({
        realm: Script.RealmSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.ContextTargetSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        sandbox: z.string().optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.TargetSchema = z.lazy(() => z.union([Script.ContextTargetSchema, Script.RealmTargetSchema]));
})(Script || (Script = {}));
(function (Script) {
    Script.AddPreloadScriptSchema = z.lazy(() => z.object({
        method: z.literal('script.addPreloadScript'),
        params: Script.AddPreloadScriptParametersSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.AddPreloadScriptParametersSchema = z.lazy(() => z.object({
        functionDeclaration: z.string(),
        arguments: z.array(Script.ChannelValueSchema).optional(),
        contexts: z
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: z.array(Browser.UserContextSchema).min(1).optional(),
        sandbox: z.string().optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.AddPreloadScriptResultSchema = z.lazy(() => z.object({
        script: Script.PreloadScriptSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.DisownSchema = z.lazy(() => z.object({
        method: z.literal('script.disown'),
        params: Script.DisownParametersSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.DisownParametersSchema = z.lazy(() => z.object({
        handles: z.array(Script.HandleSchema),
        target: Script.TargetSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.CallFunctionParametersSchema = z.lazy(() => z.object({
        functionDeclaration: z.string(),
        awaitPromise: z.boolean(),
        target: Script.TargetSchema,
        arguments: z.array(Script.LocalValueSchema).optional(),
        resultOwnership: Script.ResultOwnershipSchema.optional(),
        serializationOptions: Script.SerializationOptionsSchema.optional(),
        this: Script.LocalValueSchema.optional(),
        userActivation: z.boolean().default(false).optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.CallFunctionSchema = z.lazy(() => z.object({
        method: z.literal('script.callFunction'),
        params: Script.CallFunctionParametersSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.EvaluateSchema = z.lazy(() => z.object({
        method: z.literal('script.evaluate'),
        params: Script.EvaluateParametersSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.EvaluateParametersSchema = z.lazy(() => z.object({
        expression: z.string(),
        target: Script.TargetSchema,
        awaitPromise: z.boolean(),
        resultOwnership: Script.ResultOwnershipSchema.optional(),
        serializationOptions: Script.SerializationOptionsSchema.optional(),
        userActivation: z.boolean().default(false).optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.GetRealmsSchema = z.lazy(() => z.object({
        method: z.literal('script.getRealms'),
        params: Script.GetRealmsParametersSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.GetRealmsParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema.optional(),
        type: Script.RealmTypeSchema.optional(),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.GetRealmsResultSchema = z.lazy(() => z.object({
        realms: z.array(Script.RealmInfoSchema),
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RemovePreloadScriptSchema = z.lazy(() => z.object({
        method: z.literal('script.removePreloadScript'),
        params: Script.RemovePreloadScriptParametersSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RemovePreloadScriptParametersSchema = z.lazy(() => z.object({
        script: Script.PreloadScriptSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.MessageParametersSchema = z.lazy(() => z.object({
        channel: Script.ChannelSchema,
        data: Script.RemoteValueSchema,
        source: Script.SourceSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RealmCreatedSchema = z.lazy(() => z.object({
        method: z.literal('script.realmCreated'),
        params: Script.RealmInfoSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.MessageSchema = z.lazy(() => z.object({
        method: z.literal('script.message'),
        params: Script.MessageParametersSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RealmDestroyedSchema = z.lazy(() => z.object({
        method: z.literal('script.realmDestroyed'),
        params: Script.RealmDestroyedParametersSchema,
    }));
})(Script || (Script = {}));
(function (Script) {
    Script.RealmDestroyedParametersSchema = z.lazy(() => z.object({
        realm: Script.RealmSchema,
    }));
})(Script || (Script = {}));
export const StorageCommandSchema = z.lazy(() => z.union([
    Storage.DeleteCookiesSchema,
    Storage.GetCookiesSchema,
    Storage.SetCookieSchema,
]));
export const StorageResultSchema = z.lazy(() => z.union([
    Storage.DeleteCookiesResultSchema,
    Storage.GetCookiesResultSchema,
    Storage.SetCookieResultSchema,
]));
export var Storage;
(function (Storage) {
    Storage.PartitionKeySchema = z.lazy(() => z
        .object({
        userContext: z.string().optional(),
        sourceOrigin: z.string().optional(),
    })
        .and(ExtensibleSchema));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.GetCookiesSchema = z.lazy(() => z.object({
        method: z.literal('storage.getCookies'),
        params: Storage.GetCookiesParametersSchema,
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.CookieFilterSchema = z.lazy(() => z
        .object({
        name: z.string().optional(),
        value: Network.BytesValueSchema.optional(),
        domain: z.string().optional(),
        path: z.string().optional(),
        size: JsUintSchema.optional(),
        httpOnly: z.boolean().optional(),
        secure: z.boolean().optional(),
        sameSite: Network.SameSiteSchema.optional(),
        expiry: JsUintSchema.optional(),
    })
        .and(ExtensibleSchema));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.BrowsingContextPartitionDescriptorSchema = z.lazy(() => z.object({
        type: z.literal('context'),
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.StorageKeyPartitionDescriptorSchema = z.lazy(() => z
        .object({
        type: z.literal('storageKey'),
        userContext: z.string().optional(),
        sourceOrigin: z.string().optional(),
    })
        .and(ExtensibleSchema));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.PartitionDescriptorSchema = z.lazy(() => z.union([
        Storage.BrowsingContextPartitionDescriptorSchema,
        Storage.StorageKeyPartitionDescriptorSchema,
    ]));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.GetCookiesParametersSchema = z.lazy(() => z.object({
        filter: Storage.CookieFilterSchema.optional(),
        partition: Storage.PartitionDescriptorSchema.optional(),
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.GetCookiesResultSchema = z.lazy(() => z.object({
        cookies: z.array(Network.CookieSchema),
        partitionKey: Storage.PartitionKeySchema,
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.SetCookieSchema = z.lazy(() => z.object({
        method: z.literal('storage.setCookie'),
        params: Storage.SetCookieParametersSchema,
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.PartialCookieSchema = z.lazy(() => z
        .object({
        name: z.string(),
        value: Network.BytesValueSchema,
        domain: z.string(),
        path: z.string().optional(),
        httpOnly: z.boolean().optional(),
        secure: z.boolean().optional(),
        sameSite: Network.SameSiteSchema.optional(),
        expiry: JsUintSchema.optional(),
    })
        .and(ExtensibleSchema));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.SetCookieParametersSchema = z.lazy(() => z.object({
        cookie: Storage.PartialCookieSchema,
        partition: Storage.PartitionDescriptorSchema.optional(),
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.SetCookieResultSchema = z.lazy(() => z.object({
        partitionKey: Storage.PartitionKeySchema,
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.DeleteCookiesSchema = z.lazy(() => z.object({
        method: z.literal('storage.deleteCookies'),
        params: Storage.DeleteCookiesParametersSchema,
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.DeleteCookiesParametersSchema = z.lazy(() => z.object({
        filter: Storage.CookieFilterSchema.optional(),
        partition: Storage.PartitionDescriptorSchema.optional(),
    }));
})(Storage || (Storage = {}));
(function (Storage) {
    Storage.DeleteCookiesResultSchema = z.lazy(() => z.object({
        partitionKey: Storage.PartitionKeySchema,
    }));
})(Storage || (Storage = {}));
export const LogEventSchema = z.lazy(() => Log.EntryAddedSchema);
export var Log;
(function (Log) {
    Log.LevelSchema = z.lazy(() => z.enum(['debug', 'info', 'warn', 'error']));
})(Log || (Log = {}));
(function (Log) {
    Log.EntrySchema = z.lazy(() => z.union([
        Log.GenericLogEntrySchema,
        Log.ConsoleLogEntrySchema,
        Log.JavascriptLogEntrySchema,
    ]));
})(Log || (Log = {}));
(function (Log) {
    Log.BaseLogEntrySchema = z.lazy(() => z.object({
        level: Log.LevelSchema,
        source: Script.SourceSchema,
        text: z.union([z.string(), z.null()]),
        timestamp: JsUintSchema,
        stackTrace: Script.StackTraceSchema.optional(),
    }));
})(Log || (Log = {}));
(function (Log) {
    Log.GenericLogEntrySchema = z.lazy(() => Log.BaseLogEntrySchema.and(z.object({
        type: z.string(),
    })));
})(Log || (Log = {}));
(function (Log) {
    Log.ConsoleLogEntrySchema = z.lazy(() => Log.BaseLogEntrySchema.and(z.object({
        type: z.literal('console'),
        method: z.string(),
        args: z.array(Script.RemoteValueSchema),
    })));
})(Log || (Log = {}));
(function (Log) {
    Log.JavascriptLogEntrySchema = z.lazy(() => Log.BaseLogEntrySchema.and(z.object({
        type: z.literal('javascript'),
    })));
})(Log || (Log = {}));
(function (Log) {
    Log.EntryAddedSchema = z.lazy(() => z.object({
        method: z.literal('log.entryAdded'),
        params: Log.EntrySchema,
    }));
})(Log || (Log = {}));
export const InputCommandSchema = z.lazy(() => z.union([
    Input.PerformActionsSchema,
    Input.ReleaseActionsSchema,
    Input.SetFilesSchema,
]));
export const InputEventSchema = z.lazy(() => Input.FileDialogOpenedSchema);
export var Input;
(function (Input) {
    Input.ElementOriginSchema = z.lazy(() => z.object({
        type: z.literal('element'),
        element: Script.SharedReferenceSchema,
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.PerformActionsParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        actions: z.array(Input.SourceActionsSchema),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.NoneSourceActionsSchema = z.lazy(() => z.object({
        type: z.literal('none'),
        id: z.string(),
        actions: z.array(Input.NoneSourceActionSchema),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.KeySourceActionsSchema = z.lazy(() => z.object({
        type: z.literal('key'),
        id: z.string(),
        actions: z.array(Input.KeySourceActionSchema),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.PointerSourceActionsSchema = z.lazy(() => z.object({
        type: z.literal('pointer'),
        id: z.string(),
        parameters: Input.PointerParametersSchema.optional(),
        actions: z.array(Input.PointerSourceActionSchema),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.PerformActionsSchema = z.lazy(() => z.object({
        method: z.literal('input.performActions'),
        params: Input.PerformActionsParametersSchema,
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.SourceActionsSchema = z.lazy(() => z.union([
        Input.NoneSourceActionsSchema,
        Input.KeySourceActionsSchema,
        Input.PointerSourceActionsSchema,
        Input.WheelSourceActionsSchema,
    ]));
})(Input || (Input = {}));
(function (Input) {
    Input.NoneSourceActionSchema = z.lazy(() => Input.PauseActionSchema);
})(Input || (Input = {}));
(function (Input) {
    Input.KeySourceActionSchema = z.lazy(() => z.union([
        Input.PauseActionSchema,
        Input.KeyDownActionSchema,
        Input.KeyUpActionSchema,
    ]));
})(Input || (Input = {}));
(function (Input) {
    Input.PointerTypeSchema = z.lazy(() => z.enum(['mouse', 'pen', 'touch']));
})(Input || (Input = {}));
(function (Input) {
    Input.PointerParametersSchema = z.lazy(() => z.object({
        pointerType: Input.PointerTypeSchema.default('mouse').optional(),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.WheelSourceActionsSchema = z.lazy(() => z.object({
        type: z.literal('wheel'),
        id: z.string(),
        actions: z.array(Input.WheelSourceActionSchema),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.PointerSourceActionSchema = z.lazy(() => z.union([
        Input.PauseActionSchema,
        Input.PointerDownActionSchema,
        Input.PointerUpActionSchema,
        Input.PointerMoveActionSchema,
    ]));
})(Input || (Input = {}));
(function (Input) {
    Input.WheelSourceActionSchema = z.lazy(() => z.union([Input.PauseActionSchema, Input.WheelScrollActionSchema]));
})(Input || (Input = {}));
(function (Input) {
    Input.PauseActionSchema = z.lazy(() => z.object({
        type: z.literal('pause'),
        duration: JsUintSchema.optional(),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.KeyDownActionSchema = z.lazy(() => z.object({
        type: z.literal('keyDown'),
        value: z.string(),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.KeyUpActionSchema = z.lazy(() => z.object({
        type: z.literal('keyUp'),
        value: z.string(),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.PointerUpActionSchema = z.lazy(() => z.object({
        type: z.literal('pointerUp'),
        button: JsUintSchema,
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.PointerDownActionSchema = z.lazy(() => z
        .object({
        type: z.literal('pointerDown'),
        button: JsUintSchema,
    })
        .and(Input.PointerCommonPropertiesSchema));
})(Input || (Input = {}));
(function (Input) {
    Input.PointerMoveActionSchema = z.lazy(() => z
        .object({
        type: z.literal('pointerMove'),
        x: z.number(),
        y: z.number(),
        duration: JsUintSchema.optional(),
        origin: Input.OriginSchema.optional(),
    })
        .and(Input.PointerCommonPropertiesSchema));
})(Input || (Input = {}));
(function (Input) {
    Input.WheelScrollActionSchema = z.lazy(() => z.object({
        type: z.literal('scroll'),
        x: JsIntSchema,
        y: JsIntSchema,
        deltaX: JsIntSchema,
        deltaY: JsIntSchema,
        duration: JsUintSchema.optional(),
        origin: Input.OriginSchema.default('viewport').optional(),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.PointerCommonPropertiesSchema = z.lazy(() => z.object({
        width: JsUintSchema.default(1).optional(),
        height: JsUintSchema.default(1).optional(),
        pressure: z.number().default(0).optional(),
        tangentialPressure: z.number().default(0).optional(),
        twist: z
            .number()
            .int()
            .nonnegative()
            .gte(0)
            .lte(359)
            .default(0)
            .optional(),
        altitudeAngle: z
            .number()
            .gte(0)
            .lte(1.5707963267948966)
            .default(0)
            .optional(),
        azimuthAngle: z
            .number()
            .gte(0)
            .lte(6.283185307179586)
            .default(0)
            .optional(),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.OriginSchema = z.lazy(() => z.union([
        z.literal('viewport'),
        z.literal('pointer'),
        Input.ElementOriginSchema,
    ]));
})(Input || (Input = {}));
(function (Input) {
    Input.ReleaseActionsSchema = z.lazy(() => z.object({
        method: z.literal('input.releaseActions'),
        params: Input.ReleaseActionsParametersSchema,
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.ReleaseActionsParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.SetFilesSchema = z.lazy(() => z.object({
        method: z.literal('input.setFiles'),
        params: Input.SetFilesParametersSchema,
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.SetFilesParametersSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        element: Script.SharedReferenceSchema,
        files: z.array(z.string()),
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.FileDialogOpenedSchema = z.lazy(() => z.object({
        method: z.literal('input.fileDialogOpened'),
        params: Input.FileDialogInfoSchema,
    }));
})(Input || (Input = {}));
(function (Input) {
    Input.FileDialogInfoSchema = z.lazy(() => z.object({
        context: BrowsingContext.BrowsingContextSchema,
        element: Script.SharedReferenceSchema.optional(),
        multiple: z.boolean(),
    }));
})(Input || (Input = {}));
export const WebExtensionCommandSchema = z.lazy(() => z.union([WebExtension.InstallSchema, WebExtension.UninstallSchema]));
export const WebExtensionResultSchema = z.lazy(() => WebExtension.InstallResultSchema);
export var WebExtension;
(function (WebExtension) {
    WebExtension.ExtensionSchema = z.lazy(() => z.string());
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.InstallParametersSchema = z.lazy(() => z.object({
        extensionData: WebExtension.ExtensionDataSchema,
    }));
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.InstallSchema = z.lazy(() => z.object({
        method: z.literal('webExtension.install'),
        params: WebExtension.InstallParametersSchema,
    }));
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.ExtensionDataSchema = z.lazy(() => z.union([
        WebExtension.ExtensionArchivePathSchema,
        WebExtension.ExtensionBase64EncodedSchema,
        WebExtension.ExtensionPathSchema,
    ]));
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.ExtensionPathSchema = z.lazy(() => z.object({
        type: z.literal('path'),
        path: z.string(),
    }));
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.ExtensionArchivePathSchema = z.lazy(() => z.object({
        type: z.literal('archivePath'),
        path: z.string(),
    }));
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.ExtensionBase64EncodedSchema = z.lazy(() => z.object({
        type: z.literal('base64'),
        value: z.string(),
    }));
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.InstallResultSchema = z.lazy(() => z.object({
        extension: WebExtension.ExtensionSchema,
    }));
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.UninstallSchema = z.lazy(() => z.object({
        method: z.literal('webExtension.uninstall'),
        params: WebExtension.UninstallParametersSchema,
    }));
})(WebExtension || (WebExtension = {}));
(function (WebExtension) {
    WebExtension.UninstallParametersSchema = z.lazy(() => z.object({
        extension: WebExtension.ExtensionSchema,
    }));
})(WebExtension || (WebExtension = {}));
//# sourceMappingURL=webdriver-bidi.js.map